{"name": "free-nextadmin-nextjs", "version": "1.2.1", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/core": "^0.39.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "apexcharts": "^4.5.0", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "flatpickr": "^4.6.13", "jsvectormap": "^1.6.0", "lucide-react": "^0.515.0", "next": "^15.3.3", "next-auth": "^5.0.0-beta.28", "next-themes": "^0.4.4", "nextjs-toploader": "^3.7.15", "react": "19.0.0", "react-apexcharts": "^1.7.0", "react-dom": "19.0.0", "react-icons": "^5.5.0", "sonner": "^2.0.5", "tailwind-merge": "^2.6.0"}, "devDependencies": {"@types/node": "^22", "@types/react": "19.0.8", "@types/react-dom": "19.0.3", "autoprefixer": "^10.4.20", "eslint": "^9", "eslint-config-next": "15.1.6", "postcss": "^8", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.16", "typescript": "^5"}}