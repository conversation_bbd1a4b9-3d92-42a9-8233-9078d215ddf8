'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { Logo } from '@/components/logo';
import { useAuth, type SignupData } from '@/context/AuthContext';
import { useRouter } from 'next/navigation';

// Type definitions for form fields
interface BaseFieldConfig {
  id: string;
  label: string;
  required: boolean;
  colSpan: 1 | 2;
}

interface TextFieldConfig extends BaseFieldConfig {
  type: 'text';
  autoComplete?: string;
  placeholder: string;
}

interface EmailFieldConfig extends BaseFieldConfig {
  type: 'email';
  autoComplete?: string;
  placeholder: string;
  value?: string; // For controlled components like confirmEmail
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

interface PasswordFieldConfig extends BaseFieldConfig {
  type: 'password';
  autoComplete?: string;
  placeholder: string;
  minLength: number;
  value?: string; // For controlled components like confirmPassword
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

interface TelFieldConfig extends BaseFieldConfig {
  type: 'tel';
  autoComplete?: string;
  placeholder: string;
}

interface SelectFieldConfig extends BaseFieldConfig {
  type: 'select';
  options: Array<{ value: string; label: string }>;
}

type FormFieldConfig = 
  | TextFieldConfig 
  | EmailFieldConfig 
  | PasswordFieldConfig 
  | TelFieldConfig
  | SelectFieldConfig;
import { toast } from 'sonner';

interface FormData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  companyName: string;
  countryCode: string;
  phone: string;
  streetAddress: string;
  city: string;
  country: string;
  orgType: 'company' | 'supplier';
}

export default function SignupPage() {
  const { signup } = useAuth();
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1); // 1: User & Account, 2: Company Info
  const [formData, setFormData] = useState<FormData>({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    companyName: '',
    countryCode: '+256', // Default country code for Uganda
    phone: '',
    streetAddress: '',
    city: '',
    country: 'Uganda', // Default country set to Uganda
    orgType: 'company', // Default value
  });
  const [confirmEmail, setConfirmEmail] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    if (name === 'confirmEmail') {
      setConfirmEmail(value);
    } else if (name === 'confirmPassword') {
      setConfirmPassword(value);
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
    
    // Clear any previous error when user starts typing
    if (error) setError('');
  };

  // Validate step 1 fields and proceed to step 2 if valid
  const validateAndProceed = () => {
    // Validate required personal info fields
    if (!formData.firstName.trim()) {
      setError('First name is required');
      return;
    }
    
    if (!formData.lastName.trim()) {
      setError('Last name is required');
      return;
    }
    
    // Validate email
    if (!formData.email.trim()) {
      setError('Email address is required');
      return;
    }
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      setError('Please enter a valid email address');
      return;
    }
    
    // Validate email confirmation
    if (formData.email !== confirmEmail) {
      setError('Emails do not match');
      return;
    }
    
    // Validate password
    if (!formData.password) {
      setError('Password is required');
      return;
    }
    
    if (formData.password.length < 8) {
      setError('Password must be at least 8 characters long');
      return;
    }
    
    // Validate password confirmation
    if (formData.password !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }
    
    // If all validations pass, proceed to next step
    setCurrentStep(prev => prev + 1);
    setError('');
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // These validations are now handled in validateAndProceed function
    // but we'll keep them here as a safety measure
    if (formData.email !== confirmEmail) {
      setError('Emails do not match');
      return;
    }
    
    if (formData.password !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }
    
    if (formData.password.length < 8) {
      setError('Password must be at least 8 characters long');
      return;
    }
    
    setIsLoading(true);
    setError('');
    
    try {
      // Get current date in YYYY-MM-DD format for incorporation_date
      const today = new Date();
      const formattedDate = today.toISOString().split('T')[0];
      
      // Transform form data to match the expected API format
      // Combine the address fields into a single address string
      const fullAddress = `${formData.streetAddress}, ${formData.city}, ${formData.country}`;
      
      // Combine country code with phone number and clean it up
      const fullPhoneNumber = `${formData.countryCode}${formData.phone}`.replace(/[^0-9+]/g, '');
      
      const signupData: SignupData = {
        org_in: {
          name: formData.companyName,
          type: formData.orgType as 'company' | 'supplier',
          address: fullAddress,
          telephone: fullPhoneNumber,  // Includes country code
          contact_person: `${formData.firstName} ${formData.lastName}`,
          status: 'active',
          country: formData.country,
          incorporation_date: formattedDate
        },
        admin_in: {
          email: formData.email,
          password: formData.password,
          first_name: formData.firstName,
          last_name: formData.lastName,
          telephone_number: fullPhoneNumber,  // Also use full number with country code
          role_names: [formData.orgType === 'company' ? 'c_admin' : 's_admin']
        }
      };
      
      await signup(signupData);
      toast.success('Account created successfully! Please sign in with your credentials.');
      router.push('/login');
    } catch (err) {
      console.error('Signup error:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to create account. Please try again.';
      setError(errorMessage);
      toast.error('Failed to create account');
    } finally {
      setIsLoading(false);
    }
  };

  const inputClass = "block w-full rounded-md border-0 py-2.5 px-3 text-gray-900 dark:text-white shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-600 placeholder:text-gray-400 dark:placeholder:text-gray-500 focus:ring-2 focus:ring-inset focus:ring-ascension-blue dark:bg-gray-700 sm:text-sm sm:leading-6";
  const labelClass = "block text-sm font-medium leading-6 text-gray-900 dark:text-gray-200";

  // Form field configuration
  const personalInfoFields: FormFieldConfig[] = [
    {
      id: 'firstName',
      label: 'First name',
      type: 'text',
      autoComplete: 'given-name',
      placeholder: 'John',
      required: true,
      colSpan: 1
    },
    {
      id: 'lastName',
      label: 'Last name',
      type: 'text',
      autoComplete: 'family-name',
      placeholder: 'Doe',
      required: true,
      colSpan: 1
    },
  ];

  const accountCredentialFields: FormFieldConfig[] = [
    {
      id: 'email',
      label: 'Email address',
      type: 'email',
      autoComplete: 'email',
      placeholder: '<EMAIL>',
      required: true,
      colSpan: 1
    },
    {
      id: 'confirmEmail',
      label: 'Confirm email',
      type: 'email',
      placeholder: 'Confirm your email',
      required: true,
      colSpan: 1,
      value: confirmEmail,
      onChange: (e: React.ChangeEvent<HTMLInputElement>) => setConfirmEmail(e.target.value)
    },
    {
      id: 'password',
      label: 'Password',
      type: 'password',
      autoComplete: 'new-password',
      placeholder: '••••••••',
      required: true,
      minLength: 8,
      colSpan: 1
    },
    {
      id: 'confirmPassword',
      label: 'Confirm password',
      type: 'password',
      autoComplete: 'new-password',
      placeholder: '••••••••',
      required: true,
      minLength: 8,
      colSpan: 1,
      value: confirmPassword,
      onChange: (e: React.ChangeEvent<HTMLInputElement>) => setConfirmPassword(e.target.value)
    },
  ];

  const companyInfoFields: FormFieldConfig[] = [
    {
      id: 'companyName',
      label: 'Company name',
      type: 'text',
      autoComplete: 'organization',
      placeholder: 'Acme Inc',
      required: true,
      colSpan: 2
    },
    {
      id: 'phone',
      label: 'Phone number',
      type: 'tel',
      autoComplete: 'tel',
      placeholder: '*********',
      required: true,
      colSpan: 1
    },
    {
      id: 'orgType',
      label: 'Account type',
      type: 'select',
      required: true,
      colSpan: 1,
      options: [
        { value: 'company', label: 'Company' },
        { value: 'supplier', label: 'Supplier' }
      ]
    },
    {
      id: 'streetAddress',
      label: 'Street Address',
      type: 'text',
      autoComplete: 'street-address',
      placeholder: '123 Main St',
      required: true,
      colSpan: 2
    },
    {
      id: 'city',
      label: 'City',
      type: 'text',
      autoComplete: 'address-level2',
      placeholder: 'New York',
      required: true,
      colSpan: 1
    },
    {
      id: 'country',
      label: 'Country',
      type: 'select',
      required: true,
      colSpan: 1,
      options: [
        { value: 'Uganda', label: 'Uganda' },
        { value: 'Kenya', label: 'Kenya' },
        { value: 'Tanzania', label: 'Tanzania' },
        { value: 'Rwanda', label: 'Rwanda' },
        { value: 'Ethiopia', label: 'Ethiopia' },
        { value: 'Nigeria', label: 'Nigeria' }
      ]
    }
  ];

  const formFields: FormFieldConfig[] = [...personalInfoFields, ...accountCredentialFields, ...companyInfoFields];

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-gray-900 p-4">
      <div className="w-full max-w-3xl">
        
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
          <div className="text-center mb-8">
            <div className="text-center mb-4">
              <Link href="/" className="inline-flex items-center">
                <Logo />
              </Link>
            </div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Just a few details to get you started</h1>
          </div>
          
          {error && (
            <div className="mb-6 rounded-md bg-red-50 dark:bg-red-900/30 p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400 dark:text-red-300" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-red-800 dark:text-red-300">{error}</p>
                </div>
              </div>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="mb-4 text-center text-sm text-gray-600 dark:text-gray-400">
              Step {currentStep} of 2
            </div>

            {/* Step 1: User & Account Details */}
            {currentStep === 1 && (
              <>
                {/* Personal Information Section */}
                <section>
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">Personal Information</h2>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-6 gap-y-4">
                    {personalInfoFields.map((field) => (
                      <div key={field.id} className={field.colSpan === 2 ? 'sm:col-span-2' : ''}>
                        <label htmlFor={field.id} className={labelClass}>
                          {field.label}
                          {field.required && <span className="text-red-500">*</span>}
                        </label>
                        <div className="mt-1.5">
                          {(() => {
                            const commonProps = {
                              id: field.id,
                              name: field.id,
                              className: inputClass,
                              required: field.required,
                            };
                            switch (field.type) {
                              case 'text':
                                return <input {...commonProps} type="text" autoComplete={field.autoComplete} placeholder={field.placeholder} value={formData[field.id as keyof FormData] as string} onChange={handleInputChange} />;
                              default: return null;
                            }
                          })()}
                        </div>
                      </div>
                    ))}
                  </div>
                </section>

                {/* Account Credentials Section */}
                <section className="pt-6">
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">Account Credentials</h2>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-6 gap-y-4">
                    {accountCredentialFields.map((field) => (
                      <div key={field.id} className={field.colSpan === 2 ? 'sm:col-span-2' : ''}>
                        <label htmlFor={field.id} className={labelClass}>
                          {field.label}
                          {field.required && <span className="text-red-500">*</span>}
                        </label>
                        <div className="mt-1.5">
                          {(() => {
                            const commonProps = {
                              id: field.id,
                              name: field.id,
                              className: inputClass,
                              required: field.required,
                            };
                            switch (field.type) {
                              case 'email':
                                return <input {...commonProps} type="email" autoComplete={field.autoComplete} placeholder={field.placeholder} value={field.value !== undefined ? field.value : formData[field.id as keyof FormData] as string} onChange={field.onChange || handleInputChange} />;
                              case 'password':
                                return <input {...commonProps} type="password" autoComplete={field.autoComplete} placeholder={field.placeholder} value={field.value !== undefined ? field.value : formData[field.id as keyof FormData] as string} onChange={field.onChange || handleInputChange} minLength={field.minLength} />;
                              default: return null;
                            }
                          })()}
                        </div>
                      </div>
                    ))}
                  </div>
                </section>
              </>
            )}

            {/* Step 2: Company Information Section */}
            {currentStep === 2 && (
              <section>
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">Company Information</h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-6 gap-y-4">
                  {companyInfoFields.map((field) => (
                    <div key={field.id} className={field.colSpan === 2 ? 'sm:col-span-2' : ''}>
                      <label htmlFor={field.id} className={labelClass}>
                        {field.label}
                        {field.required && <span className="text-red-500">*</span>}
                      </label>
                      <div className="mt-1.5">
                        {(() => {
                          const commonProps = {
                            id: field.id,
                            name: field.id,
                            className: inputClass,
                            required: field.required,
                          };
                          switch (field.type) {
                            case 'text':
                              return <input {...commonProps} type="text" autoComplete={field.autoComplete} placeholder={field.placeholder} value={formData[field.id as keyof FormData]as string} onChange={handleInputChange} />;
                            case 'tel':
                              return (
                                <div className="flex">
                                  {field.id === 'phone' && (
                                    <div className="w-24 flex-shrink-0">
                                      <select
                                        id="countryCode"
                                        name="countryCode"
                                        className={`${inputClass} rounded-r-none border-r-0`}
                                        value={formData.countryCode}
                                        onChange={handleInputChange}
                                      >
                                        <option value="+256">+256</option>
                                        <option value="+254">+254</option>
                                        <option value="+255">+255</option>
                                        <option value="+250">+250</option>
                                        <option value="+251">+251</option>
                                        <option value="+234">+234</option>
                                      </select>
                                    </div>
                                  )}
                                  <input 
                                    {...commonProps} 
                                    type="tel" 
                                    autoComplete={field.autoComplete} 
                                    placeholder={field.placeholder} 
                                    value={formData[field.id as keyof FormData] as string} 
                                    onChange={handleInputChange} 
                                    className={`${inputClass} ${field.id === 'phone' ? 'rounded-l-none' : ''} flex-1`}
                                  />
                                </div>
                              );
                            case 'select':
                              return (
                                <select {...commonProps} value={formData[field.id as keyof FormData] as string} onChange={handleInputChange}>
                                  {field.options.map(option => (
                                    <option key={option.value} value={option.value}>{option.label}</option>
                                  ))}
                                </select>
                              );
                            default: return null;
                          }
                        })()}
                      </div>
                    </div>
                  ))}
                </div>
              </section>
            )}

            <div className="pt-2 flex justify-between items-center">
              {currentStep > 1 && (
                <button
                  type="button"
                  onClick={() => setCurrentStep(prev => prev - 1)}
                  className="py-2.5 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#14546c] dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600"
                >
                  Previous
                </button>
              )}
              {currentStep < 2 && (
                <button
                  type="button"
                  onClick={() => validateAndProceed()}
                  className="ml-auto py-2.5 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#14546c] hover:bg-[#0e3e50] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#14546c] dark:bg-[#14546c] dark:hover:bg-[#14546c]"
                >
                  Next
                </button>
              )}
              {currentStep === 2 && (
                <button
                  type="submit"
                  disabled={isLoading}
                  className="ml-auto py-2.5 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#14546c] hover:bg-[#0e3e50] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#14546c] dark:bg-[#14546c] dark:hover:bg-[#14546c]"
                >
                  {isLoading ? 'Creating Account...' : 'Sign Up'}
                </button>
              )}
            </div>
          </form>

          <p className="mt-6 text-center text-sm text-gray-600 dark:text-gray-400">
            Already have an Account?{' '}
            <a href="/login" className="font-semibold leading-6 text-ascension-blue hover:text-ascension-blue-dark dark:text-blue-400 dark:hover:text-blue-300">Log in</a>
          </p>
        </div>
      </div>
    </div>
  );
}
