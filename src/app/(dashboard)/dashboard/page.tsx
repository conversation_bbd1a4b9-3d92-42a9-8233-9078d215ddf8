'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';

export default function DashboardPage() {
  const { user, isAuthenticated } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (isAuthenticated === false) {
      console.log('Dashboard: Not authenticated, redirecting to login');
      router.push('/login');
      return;
    }

    if (isAuthenticated === true && user) {
      console.log('Dashboard: User is authenticated, redirecting based on role');
      // Redirect based on user role
      if (user.roles?.includes('s_admin')) {
        router.push('/supplier-dashboard');
      } else if (user.roles?.includes('c_admin')) {
        router.push('/company-dashboard');
      } else {
        // Default fallback if role is not recognized
        console.warn('Unknown user role, defaulting to company dashboard');
        router.push('/company-dashboard');
      }
    }
  }, [isAuthenticated, router, user]);

  // Show loading state while redirecting
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
        <p className="text-gray-600">Loading your dashboard...</p>
      </div>
    </div>
  );
}
