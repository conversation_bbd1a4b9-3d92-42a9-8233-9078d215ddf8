import axios, { AxiosError } from 'axios';

// Define the API URL directly since we're having issues with the config import
const API_URL = 'https://dev.ascensionservices.net/api/v1';

// Simple interface for the response data
interface TokenResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
}

interface User {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  is_active: boolean;
  role_names: string[];
  organisation_id: number;
}

// Create a simple axios instance with minimal configuration
const apiClient = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  timeout: 30000, // 30 seconds timeout
  withCredentials: false, // Disable credentials for now
  validateStatus: (status) => status < 500 // Don't throw for 4xx errors
});

interface LoginData {
  email: string;
  password: string;
}

interface OrgData {
  type: 'supplier' | 'company';
  name: string;
  incorporation_date: string;
  address: string;
  telephone: string;
  contact_person: string;
  status: string;
  country?: string; // Added for new signup form design
}

interface AdminData {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  telephone_number: string;
  role_names: ('c_admin' | 's_admin')[];
}

interface SignupData {
  org_in: OrgData;
  admin_in: AdminData;
}

const USER_API_URL = 'https://dev.ascensionservices.net/api/v1/auth/users';

export interface IAuthService {
  signup(signupData: SignupData): Promise<TokenResponse>;
  login(credentials: LoginData): Promise<TokenResponse>;
  logout(): void;
  getAuthHeader(): { Authorization: string } | {};
  isAuthenticated(): boolean;
}

export const AuthService: IAuthService = {
  async signup(signupData: SignupData): Promise<TokenResponse> {
    try {
      // Format the request data to match the working test format
      const requestData = {
        org_in: {
          name: signupData.org_in.name,
          type: signupData.org_in.type,
          address: signupData.org_in.address,
          telephone: String(signupData.org_in.telephone || '').trim(),
          contact_person: signupData.org_in.contact_person,
          status: 'active',
          country: signupData.org_in.country || 'Uganda',
          incorporation_date: new Date().toISOString().split('T')[0]
        },
        admin_in: {
          email: signupData.admin_in.email,
          password: signupData.admin_in.password,
          first_name: signupData.admin_in.first_name,
          last_name: signupData.admin_in.last_name,
          telephone_number: String(signupData.admin_in.telephone_number || '').trim(),
          role_names: signupData.admin_in.role_names
        }
      };
      
      console.log('Prepared signup request data:', JSON.stringify(requestData, null, 2));
      
      // Log the prepared request data
      console.log('Sending request to:', `${API_URL}/auth/signup`);
      console.log('Request data:', JSON.stringify(requestData, null, 2));
      
      let createResponse;
      try {
        // Simple POST request with minimal configuration
        createResponse = await apiClient.post('/auth/signup', requestData, {
          headers: {
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Expires': '0'
          }
        });
        
        console.log('Raw response:', {
          status: createResponse.status,
          statusText: createResponse.statusText,
          headers: createResponse.headers,
          data: createResponse.data
        });
        
        // If we get here but status is not 2xx, handle it as an error
        if (createResponse.status < 200 || createResponse.status >= 300) {
          const error = new Error(`Request failed with status ${createResponse.status}`) as any;
          error.response = {
            data: createResponse.data,
            status: createResponse.status,
            statusText: createResponse.statusText,
            headers: createResponse.headers,
            config: {
              url: `${API_URL}/signup`,
              method: 'post',
              data: requestData
            }
          };
          throw error;
        }
        
        console.log('Signup successful. Response:', createResponse.data);
        
      } catch (error: any) {
        console.error('Detailed error during signup:', {
          message: error.message,
          response: {
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data,
            headers: error.response?.headers
          },
          request: {
            method: error.request?.method,
            url: error.request?.path,
            data: error.request?.data
          }
        });
        throw error;
      }
      
      // If user creation was successful, log them in

      // If user creation was successful, log them in
      const formBody = new URLSearchParams();
      formBody.append('grant_type', 'password');
      formBody.append('username', signupData.admin_in.email);
      formBody.append('password', signupData.admin_in.password);
      formBody.append('scope', '');
      formBody.append('client_id', 'string');
      formBody.append('client_secret', 'string');

      const loginResponse = await axios.post<TokenResponse>(
        `${API_URL}/login`,
        formBody,
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'accept': 'application/json'
          }
        }
      );
      
      const { access_token, refresh_token } = loginResponse.data;
      
      // Store the tokens
      localStorage.setItem('access_token', access_token);
      if (refresh_token) {
        localStorage.setItem('refreshToken', refresh_token);
      }

      // Fetch and store user details
      try {
        const userResponse = await axios.get(`${USER_API_URL}/me`, {
          headers: {
            'Authorization': `Bearer ${access_token}`,
            'accept': 'application/json',
          },
        });
        // The backend returns snake_case, AuthContext expects camelCase for names
        const userData = {
          ...userResponse.data,
          firstName: userResponse.data.first_name,
          lastName: userResponse.data.last_name,
          roles: userResponse.data.role_names || [], // Ensure roles is an array
        };
        delete userData.first_name; // remove snake_case version
        delete userData.last_name; // remove snake_case version
        delete userData.role_names;
        localStorage.setItem('user', JSON.stringify(userData));
      } catch (userError) {
        console.error('Error fetching user data after signup:', userError);
        // Decide if signup should fail if user data fetch fails, or proceed
        // For now, we'll proceed, but token is stored.
      }
      
      return createResponse.data;
    } catch (error: any) {
      // Create a copy of the error with only the data we want to log
      const errorToLog: any = {
        name: error?.name,
        message: error?.message,
        stack: error?.stack,
        isAxiosError: error?.isAxiosError
      };
      
      // Add response data if it exists
      if (error?.response) {
        errorToLog.response = {
          status: error.response.status,
          statusText: error.response.statusText,
          data: error.response.data
        };
      }
      
      // Add request data if it exists
      if (error?.config) {
        errorToLog.request = {
          url: error.config.url,
          method: error.config.method,
          data: error.config.data
        };
      }
      
      // Log the error in multiple ways to ensure we capture it
      console.error('=== SIGNUP ERROR ===');
      console.error('Error object:', error);
      console.error('Error details:', errorToLog);
      console.error('Stringified error:', JSON.stringify(errorToLog, null, 2));
      
      // Try to log the raw response data
      try {
        if (error?.response?.data) {
          console.error('Raw response data:', error.response.data);
        }
      } catch (e) {
        console.error('Could not log raw response data:', e);
      }
      
      // Clear tokens if signup fails partway through
      localStorage.removeItem('access_token');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('user');
      
      // Create a more helpful error message
      let errorMessage = 'Signup failed. Please check your information and try again.';
      
      if (axios.isAxiosError(error)) {
        const response = error.response;
        
        if (response?.data) {
          if (typeof response.data === 'object') {
            if (response.data.detail) {
              errorMessage = `Signup failed: ${JSON.stringify(response.data.detail)}`;
            } else if (response.data.message) {
              errorMessage = `Signup failed: ${response.data.message}`;
            } else if (response.data.errors) {
              errorMessage = 'Validation error. Please check all fields.';
            }
          } else if (typeof response.data === 'string') {
            errorMessage = `Server error: ${response.data}`;
          }
        }
        
        if (response?.status === 400) {
          errorMessage = 'Invalid request. Please check all fields and try again.';
        } else if (response?.status === 409) {
          errorMessage = 'An account with this email already exists.';
        }
      }
      
      console.error('Final error message:', errorMessage);
      throw new Error(errorMessage);
    }
  },

  async login(credentials: LoginData): Promise<TokenResponse> {
    console.group('=== AUTH SERVICE LOGIN FLOW ===');
    console.log('1. Starting login for:', credentials.email);
    try {
      // 1. Create login request
      const formBody = new URLSearchParams();
      formBody.append('grant_type', 'password');
      formBody.append('username', credentials.email);
      formBody.append('password', credentials.password);
      formBody.append('scope', '');
      formBody.append('client_id', 'string');
      formBody.append('client_secret', 'string');

      console.log('1. Starting login for:', credentials.email);
      
      // 2. Get authentication tokens
      console.log('2. Requesting tokens from:', `${API_URL}/auth/login`);
      console.log('2.1 Request headers:', {
        'Content-Type': 'application/x-www-form-urlencoded',
        'accept': 'application/json'
      });
      console.log('2.2 Request body (password hidden):', 
        `grant_type=password&username=${credentials.email}&password=***&scope=&client_id=string&client_secret=string`);
      const loginResponse = await fetch(`${API_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'accept': 'application/json',
        },
        body: formBody,
      });

      console.log('3. Login response status:', loginResponse.status);
      
      if (!loginResponse.ok) {
        const errorText = await loginResponse.text();
        console.error('Login failed with status:', loginResponse.status);
        console.error('Response text:', errorText);
        throw new Error('Login failed');
      }

      const tokens = await loginResponse.json();
      console.log('3.1 Token response:', {
        hasAccessToken: !!tokens.access_token,
        tokenType: tokens.token_type,
        expiresIn: tokens.expires_in
      });
      
      const { access_token, refresh_token } = tokens;
      
      console.log('4. Login successful');
      console.log('Access token present:', !!access_token);
      
      // Store tokens
      localStorage.setItem('access_token', access_token);
      if (refresh_token) {
        localStorage.setItem('refreshToken', refresh_token);
      }

      // 3. Get user data
      try {
        console.log('4. Fetching user data...');
        console.log('4.1 Using access token (first 20 chars):', 
          access_token ? `${access_token.substring(0, 20)}...` : 'No token');
        
        // First, try to get current user's data directly if possible
        try {
          console.log('4.2 Trying to fetch current user data...');
          const userResponse = await fetch(`${API_URL}/auth/users/me`, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${access_token}`,
              'accept': 'application/json',
            }
          });

          if (userResponse.ok) {
            const userData = await userResponse.json();
            console.log('4.3 Successfully fetched current user data:', userData);
            
            // Transform and store user data
            const transformedUser = {
              id: String(userData.id || 'unknown'),
              email: userData.email || credentials.email,
              firstName: userData.first_name || userData.firstName || '',
              lastName: userData.last_name || userData.lastName || '',
              roles: Array.isArray(userData.roles) ? userData.roles : 
                   (userData.role_names || [])
            };
            
            localStorage.setItem('user', JSON.stringify(transformedUser));
            console.log('4.4 Stored user data:', transformedUser);
            console.groupEnd();
            return tokens;
          }
          console.log('4.3 /me endpoint not available, falling back to /users');
        } catch (meError) {
          console.log('4.3 Error fetching /me endpoint, falling back to /users:', meError);
        }
        
        // Fallback to fetching all users if /me endpoint fails
        console.log('5. Fetching all users...');
        const userResponse = await fetch(`${API_URL}/auth/users`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${access_token}`,
            'accept': 'application/json',
          }
        });

        const responseText = await userResponse.text();
        console.log('5.1 Raw users response:', responseText);
        
        if (!userResponse.ok) {
          console.error('Failed to fetch users:', {
            status: userResponse.status,
            statusText: userResponse.statusText,
            headers: Object.fromEntries(userResponse.headers.entries())
          });
          throw new Error(`Failed to fetch users: ${userResponse.status}`);
        }
        
        // Parse response
        const users = JSON.parse(responseText);
        console.log('6. Raw users data from API:', {
          type: typeof users,
          isArray: Array.isArray(users),
          keys: users ? Object.keys(users) : 'null/undefined',
          firstItemKeys: Array.isArray(users) && users[0] ? Object.keys(users[0]) : 'N/A',
          firstItemSample: Array.isArray(users) && users[0] ? 
            JSON.stringify(users[0], Object.getOwnPropertyNames(users[0]).filter(k => !k.startsWith('_')), 2) : 'N/A'
        });
        
        if (!Array.isArray(users)) {
          console.error('Expected array of users but got:', typeof users, users);
          throw new Error('Invalid users data format - expected array');
        }
        
        // Find current user by email (case-insensitive match)
        const currentUser = users.find((u: any) => 
          u.email && u.email.toLowerCase() === credentials.email.toLowerCase()
        );

        if (!currentUser) {
          console.warn('User not found in users list. Available emails:', 
            users.map((u: any) => u.email).filter(Boolean));
          throw new Error('User not found in system');
        }

        console.log('7. Current user found:', JSON.stringify(currentUser, null, 2));
        
        // Helper to safely extract roles from different possible structures
        const extractRoles = (user: any): string[] => {
          if (Array.isArray(user.roles)) return user.roles;
          if (Array.isArray(user.role_names)) return user.role_names;
          if (user.roles && typeof user.roles === 'string') return [user.roles];
          if (user.role_names && typeof user.role_names === 'string') return [user.role_names];
          return [];
        };

        // Transform API response to match our User interface
        const userData = {
          id: String(currentUser.id || 'unknown'),
          email: currentUser.email || credentials.email,
          // Handle both snake_case and camelCase from API
          firstName: currentUser.first_name || currentUser.firstName || '',
          lastName: currentUser.last_name || currentUser.lastName || '',
          roles: extractRoles(currentUser)
        };
        
        console.log('8. Transformed user data:', JSON.stringify(userData, null, 2));
        
        if (!userData.firstName && userData.email) {
          // Extract name from email as fallback
          const nameFromEmail = userData.email.split('@')[0].split('.');
          if (nameFromEmail.length > 1) {
            userData.firstName = nameFromEmail[0];
            userData.lastName = nameFromEmail[1];
          } else {
            userData.firstName = nameFromEmail[0];
          }
          console.log('9. Added name from email:', userData.firstName, userData.lastName);
        }
        
        localStorage.setItem('user', JSON.stringify(userData));
        console.log('10. Stored user data in localStorage');
      } catch (userError) {
        console.error('Error fetching user data:', userError);
        // Continue with minimal user data
        const minimalUser = {
          id: 'unknown',
          email: credentials.email,
          firstName: '',
          lastName: '',
          roles: [],
        };
        localStorage.setItem('user', JSON.stringify(minimalUser));
      }

      console.groupEnd();
      return tokens;
    } catch (error) {
      console.error('Login process failed:', error);
      throw error;
    }
  },

  logout(): void {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('user');
  },

  getAuthHeader(): { Authorization: string } | {} {
    const token = localStorage.getItem('access_token');
    return token ? { Authorization: `Bearer ${token}` } : {};
  },

  isAuthenticated(): boolean {
    return !!localStorage.getItem('access_token');
  }
};
