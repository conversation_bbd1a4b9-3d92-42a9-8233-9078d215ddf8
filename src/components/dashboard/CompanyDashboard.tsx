'use client';

import React from 'react';
import { User } from '@/context/AuthContext';
import Link from 'next/link';

interface CompanyDashboardProps {
  user: User;
}

const CompanyDashboard: React.FC<CompanyDashboardProps> = ({ user }) => {
  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <div>
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-white">
            Hi {user.firstName || 'Admin'},
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {new Date().toLocaleDateString('en-US', { weekday: 'long', day: 'numeric', month: 'long', year: 'numeric' })} • Here is a quick snapshot of your purchases.
          </p>
        </div>
        <button className="px-4 py-2 bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 text-white text-sm font-medium rounded-md flex items-center transition-colors">
          <span className="mr-1">+</span> Create New
        </button>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3">
        {/* Submitted Requisitions */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 hover:shadow-md transition-shadow">
          <div className="flex items-center mb-2">
            <div className="p-2 rounded-full bg-amber-100 dark:bg-amber-900/30 mr-3">
              <svg className="h-8  w-8 text-amber-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <p className="text-sm font-medium text-gray-600 dark:text-gray-300">Submitted Requisitions</p>
          </div>
          <p className="text-3xl font-bold text-gray-900 dark:text-white ml-1">85</p>
          <div className="mt-2">
            <Link href="#" className="text-xs text-blue-600 dark:text-blue-400 hover:underline flex items-center transition-colors">
              View all
              <svg className="ml-1 w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Link>
          </div>
        </div>

        {/* Quotations */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 hover:shadow-md transition-shadow">
          <div className="flex items-center mb-2">
            <div className="p-2 rounded-full bg-blue-100 dark:bg-blue-900/30 mr-3">
              <svg className="h-8  w-8 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
            </div>
            <p className="text-sm font-medium text-gray-600 dark:text-gray-300">Quotations</p>
          </div>
          <p className="text-3xl font-bold text-gray-900 dark:text-white ml-1">132</p>
          <div className="mt-2">
            <Link href="#" className="text-xs text-blue-600 dark:text-blue-400 hover:underline flex items-center transition-colors">
              View Details
              <svg className="ml-1 w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Link>
          </div>
        </div>

        {/* For Approval */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 hover:shadow-md transition-shadow">
          <div className="flex items-center mb-2">
            <div className="p-2 rounded-full bg-green-100 dark:bg-green-900/30 mr-3">
              <svg className="h-8  w-8 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
            </div>
            <p className="text-sm font-medium text-gray-600 dark:text-gray-300">For Approval</p>
          </div>
          <p className="text-3xl font-bold text-gray-900 dark:text-white ml-1">63</p>
          <div className="mt-2">
            <Link href="#" className="text-xs text-blue-600 dark:text-blue-400 hover:underline flex items-center transition-colors">
              View Details
              <svg className="ml-1 w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Link>
          </div>
        </div>

        {/* Purchase Orders */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 hover:shadow-md transition-shadow">
          <div className="flex items-center mb-2">
            <div className="p-2 rounded-full bg-purple-100 dark:bg-purple-900/30 mr-3">
              <svg className="h-8  w-8 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
              </svg>
            </div>
            <p className="text-sm font-medium text-gray-600 dark:text-gray-300">Purchase Orders</p>
          </div>
          <p className="text-3xl font-bold text-gray-900 dark:text-white ml-1">55</p>
          <div className="mt-2">
            <Link href="#" className="text-xs text-blue-600 dark:text-blue-400 hover:underline flex items-center transition-colors">
              View Details
              <svg className="ml-1 w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Link>
          </div>
        </div>

        {/* Invoices */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 hover:shadow-md transition-shadow">
          <div className="flex items-center mb-2">
            <div className="p-2 rounded-full bg-red-100 dark:bg-red-900/30 mr-3">
              <svg className="h-8  w-8 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <p className="text-sm font-medium text-gray-600 dark:text-gray-300">Invoices</p>
          </div>
          <p className="text-3xl font-bold text-gray-900 dark:text-white ml-1">61</p>
          <div className="mt-2">
            <Link href="#" className="text-xs text-blue-600 dark:text-blue-400 hover:underline flex items-center transition-colors">
              View Details
              <svg className="ml-1 w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Link>
          </div>
        </div>
      </div>

      {/* Assets & Inventory Section */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 hover:shadow-md transition-shadow">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Assets & Inventory</h3>
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">Check stock levels, create and approve stores requisitions</p>
          
          {/* Store Locations */}
          <div className="space-y-4">
            {/* Central Stores */}
            <div className="flex items-center">
              <div className="p-2 bg-blue-50 dark:bg-blue-900/30 rounded-md mr-3">
                <svg className="h-8  w-8 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <div>
                <p className="font-medium text-gray-800 dark:text-gray-200">Central Stores - Kampala</p>
                <p className="text-xs text-gray-500 dark:text-gray-400">24 Luthuli Avenue, Bugolobi - Kampala</p>
              </div>
            </div>
            
            {/* Mpigi Stores */}
            <div className="flex items-center">
              <div className="p-2 bg-blue-50 dark:bg-blue-900/30 rounded-md mr-3">
                <svg className="h-8  w-8 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <div>
                <p className="font-medium text-gray-800 dark:text-gray-200">Mpigi Stores</p>
                <p className="text-xs text-gray-500 dark:text-gray-400">41 Kiwamirembe Lane, Mpigi</p>
              </div>
            </div>
            
            {/* Lira Stores */}
            <div className="flex items-center">
              <div className="p-2 bg-blue-50 dark:bg-blue-900/30 rounded-md mr-3">
                <svg className="h-8  w-8 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <div>
                <p className="font-medium text-gray-800 dark:text-gray-200">Lira Stores</p>
                <p className="text-xs text-gray-500 dark:text-gray-400">12 Obote Road, Lira</p>
              </div>
            </div>
            
            {/* Mbale Stores */}
            <div className="flex items-center">
              <div className="p-2 bg-blue-50 dark:bg-blue-900/30 rounded-md mr-3">
                <svg className="h-8  w-8 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <div>
                <p className="font-medium text-gray-800 dark:text-gray-200">Mbale Stores</p>
                <p className="text-xs text-gray-500 dark:text-gray-400">61 Namatala Road, Mbale</p>
              </div>
            </div>
            
            <div className="mt-4">
              <Link href="#" className="text-sm text-blue-600 dark:text-blue-400 hover:underline flex items-center transition-colors">
                View All
                <svg className="ml-1 w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
              </Link>
            </div>
          </div>
        </div>
        
        {/* Budget Performance */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900">Budget Performance</h3>
            <svg className="h-8  w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          
          <div className="flex justify-between mb-6">
            <div className="flex items-center">
              <div className="w-3 h-3 rounded-full bg-red-400 mr-2"></div>
              <div>
                <p className="text-xs text-gray-500">Total Expenditure</p>
                <p className="text-lg font-bold text-gray-900">$252,060</p>
              </div>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 rounded-full bg-blue-400 mr-2"></div>
              <div>
                <p className="text-xs text-gray-500">Budget Balance</p>
                <p className="text-lg font-bold text-gray-900">$23,205</p>
              </div>
            </div>
            <div className="text-right">
              <button className="text-xs text-gray-500 flex items-center">
                USD
                <svg className="h-4 w-4 inline-block text-gray-400 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
            </div>
          </div>
          
          {/* Budget Performance Chart */}
          <div className="h-64 relative">
            {/* This is a placeholder for the actual chart that would be implemented with a charting library */}
            <div className="absolute inset-0 bg-gradient-to-b from-red-100 to-transparent opacity-40"></div>
            <div className="absolute inset-0 bg-gradient-to-t from-blue-100 to-transparent opacity-40"></div>
            
            {/* Chart lines */}
            <div className="absolute inset-0">
              {/* Red line (expenditure) */}
              <svg className="w-full h-full" viewBox="0 0 400 200" preserveAspectRatio="none">
                <path 
                  d="M0,40 C50,30 100,100 150,120 C200,140 250,80 300,100 C350,120 400,180 400,180" 
                  stroke="#F87171" 
                  fill="none" 
                  strokeWidth="2"
                />
              </svg>
              
              {/* Blue line (budget) */}
              <svg className="w-full h-full" viewBox="0 0 400 200" preserveAspectRatio="none">
                <path 
                  d="M0,170 C50,160 100,150 150,140 C200,130 250,120 300,90 C350,60 400,40 400,40" 
                  stroke="#60A5FA" 
                  fill="none" 
                  strokeWidth="2"
                />
              </svg>
            </div>
            
            {/* X-axis labels */}
            <div className="absolute bottom-0 w-full flex justify-between text-xs text-gray-400 px-2">
              <span>Jan</span>
              <span>Feb</span>
              <span>Mar</span>
              <span>Apr</span>
              <span>May</span>
              <span>Jun</span>
              <span>Jul</span>
              <span>Aug</span>
            </div>
            
            {/* Y-axis labels */}
            <div className="absolute left-0 h-full flex flex-col justify-between text-xs text-gray-400 py-2">
              <span>$0</span>
              <span>$50k</span>
              <span>$100k</span>
              <span>$150k</span>
              <span>$200k</span>
              <span>$250k</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CompanyDashboard;
