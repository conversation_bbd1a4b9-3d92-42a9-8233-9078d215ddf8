'use client';

import React from 'react';
import { User } from '@/context/AuthContext';
import {
  FiClock, FiDownload, FiTag, FiCheckCircle, FiClipboard, FiFileText, 
  FiShield, FiShoppingBag, FiChevronRight, FiChevronDown, FiInfo
} from 'react-icons/fi';

interface MetricCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  iconBgColor: string;
  iconTextColor: string;
}

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  icon,
  iconBgColor,
  iconTextColor,
}) => (
  <div 
    className={`bg-white dark:bg-slate-800 rounded-lg shadow p-4 flex flex-col justify-between border-t-4 border-transparent hover:border-blue-500 dark:hover:border-blue-600 transition-colors duration-200 ease-in-out`}
  >
    <div className="flex items-start">
      <div className={`p-2 rounded-full ${iconBgColor} ${iconTextColor} mr-3`}>
        {icon}
      </div>
      <div>
        <p className="text-sm text-gray-500 dark:text-gray-400">{title}</p>
        <p className="text-2xl font-bold text-gray-800 dark:text-gray-100">{value}</p>
      </div>
    </div>
    <div className="mt-3 -mx-4 -mb-4 p-3 bg-gray-50 dark:bg-slate-700 text-gray-500 dark:text-gray-300 hover:bg-blue-500 hover:text-white dark:hover:bg-blue-600 dark:hover:text-white text-sm font-medium text-center rounded-b-lg transition-colors duration-200 ease-in-out cursor-pointer">
      View details
      <FiChevronRight className="inline ml-1 h-4 w-4 align-middle" />
    </div>
  </div>
);

const PurchaseCard: React.FC<{ title: string; value: string | number; icon: React.ReactNode }> = ({ title, value, icon }) => (
  <div className="bg-white dark:bg-slate-800 rounded-lg shadow p-4 flex items-center border-t-4 border-transparent hover:border-blue-500 dark:hover:border-blue-600 hover:shadow-md transition-all duration-200 ease-in-out cursor-pointer">
    <div className="p-2 bg-green-100 dark:bg-green-900/50 text-green-600 dark:text-green-400 rounded-md mr-3">
      {icon}
    </div>
    <div>
      <p className="text-xs text-gray-500 dark:text-gray-400">{title}</p>
      <p className="text-xl font-semibold text-gray-800 dark:text-gray-100">{value}</p>
    </div>
  </div>
);

interface SupplierDashboardProps {
  user: User;
}

const SupplierDashboard: React.FC<SupplierDashboardProps> = ({ user }) => {
  const [selectedCurrency, setSelectedCurrency] = React.useState<'USD' | 'UGX' | 'KES'>('USD');
  const [isCurrencyDropdownOpen, setIsCurrencyDropdownOpen] = React.useState(false);
  const currencies: Array<'USD' | 'UGX' | 'KES'> = ['USD', 'UGX', 'KES'];
  const currentDate = new Date().toLocaleDateString('en-US', { 
    weekday: 'long', 
    day: 'numeric', 
    month: 'long', 
    year: 'numeric' 
  });

  return (
    <div className="p-6">
      {/* Header with Greeting and Create Button */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">Hi {user?.firstName || 'Supplier Admin'},</h1>
          <div className="flex items-baseline space-x-2 text-sm">
            <p className="text-gray-500 dark:text-gray-400">{currentDate}</p>
            <span className="text-gray-400 dark:text-gray-600">·</span>
            <p className="text-gray-400 dark:text-gray-500">Here is a quick snapshot of your business</p>
          </div>
        </div>
        <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 transition-colors flex items-center">
          <span className="mr-2 text-xl">+</span> Create New
        </button>
      </div>

      {/* Top Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <MetricCard 
          title="Pending Inquiries" 
          value={35} 
          icon={<FiClock className="h-8  w-8" />} 
          iconBgColor="bg-orange-100"
          iconTextColor="text-orange-500"
        />
        <MetricCard 
          title="Sales Orders" 
          value={17} 
          icon={<FiDownload className="h-8  w-8" />} 
          iconBgColor="bg-blue-100"
          iconTextColor="text-blue-500"
        />
        <MetricCard 
          title="Sales Invoices" 
          value={22} 
          icon={<FiTag className="h-8  w-8" />} 
          iconBgColor="bg-pink-100"
          iconTextColor="text-pink-500"
        />
        <MetricCard 
          title="Closed Cases" 
          value={35} 
          icon={<FiCheckCircle className="h-8  w-8" />} 
          iconBgColor="bg-green-100"
          iconTextColor="text-green-500"
        />
      </div>

      {/* Bottom Section */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Purchases Section */}
        <div className="lg:col-span-2 space-y-4">
          <div className="flex justify-between items-center mb-2">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Purchases</h2>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <PurchaseCard 
              title="Pending Requisitions" 
              value="04" 
              icon={<FiClipboard className="h-8  w-8" />} 
            />
            <PurchaseCard 
              title="Quotations - In" 
              value="13" 
              icon={<FiFileText className="h-8  w-8" />} 
            />
            <PurchaseCard 
              title="Approved" 
              value="07" 
              icon={<FiShield className="h-8  w-8" />} 
            />
            <PurchaseCard 
              title="Invoices - In" 
              value="02" 
              icon={<FiFileText className="h-8  w-8" />}
            />
            <PurchaseCard 
              title="Purchase Orders" 
              value="09" 
              icon={<FiShoppingBag className="h-8  w-8" />}
            />
          </div>
        </div>

        {/* Sales Performance Section */}
        <div className="lg:col-span-2 bg-white dark:bg-slate-800 rounded-lg shadow p-6">
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mr-2">Budget Performance</h2>
              <FiInfo className="h-4 w-4 text-gray-400 dark:text-gray-500 cursor-pointer" title="Sales performance details"/>
            </div>
            <div className="relative">
              <button 
                onClick={() => setIsCurrencyDropdownOpen(!isCurrencyDropdownOpen)}
                className="text-xs text-gray-600 bg-gray-100 hover:bg-gray-200 dark:bg-slate-700 dark:text-gray-300 dark:hover:bg-slate-600 px-3 py-1.5 rounded-md flex items-center"
              >
                {selectedCurrency}
                <FiChevronDown className={`ml-1 h-3 w-3 transition-transform duration-200 ${isCurrencyDropdownOpen ? 'rotate-180' : ''}`} />
              </button>
              {isCurrencyDropdownOpen && (
                <div className="absolute right-0 mt-2 w-24 bg-white dark:bg-slate-900 rounded-md shadow-lg z-10">
                  {currencies.map((currency) => (
                    <a
                      key={currency}
                      href="#"
                      onClick={(e) => {
                        e.preventDefault();
                        setSelectedCurrency(currency);
                        setIsCurrencyDropdownOpen(false);
                      }}
                      className="block px-4 py-2 text-xs text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-slate-800"
                    >
                      {currency}
                    </a>
                  ))}
                </div>
              )}
            </div>
          </div>
          
          <div className="flex items-start space-x-6 py-6">
            <div className="flex-1">
              <p className="text-xs text-gray-500 dark:text-gray-400 flex items-center">
                <span className="h-2 w-2 bg-cyan-600 rounded-full mr-2"></span>
                Total Expenditure
              </p>
              <p className="text-2xl font-bold text-gray-800 dark:text-gray-100 mt-1">$252,060</p>
            </div>
            <div className="h-10 border-l border-gray-200 dark:border-gray-700"></div>
            <div className="flex-1">
              <p className="text-xs text-gray-500 dark:text-gray-400 flex items-center">
                <span className="h-2 w-2 bg-orange-400 rounded-full mr-2"></span>
                Budget Balance
              </p>
              <p className="text-2xl font-bold text-gray-800 dark:text-gray-100 mt-1">$23,205</p>
            </div>
          </div>

          {/* Chart Placeholder */}
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <p className="text-center text-gray-400 dark:text-gray-500 text-sm">[Budget Performance Chart Placeholder]</p>
            {/* Replace this div with your chart component */}
            <div className="h-48 flex items-center justify-center bg-gray-50 dark:bg-slate-700/50 rounded-md">
                Line chart will be here
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SupplierDashboard;
