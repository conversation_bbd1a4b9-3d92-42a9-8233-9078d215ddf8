'use client';

import React from 'react';
import { User } from '@/context/AuthContext';
import {
  FiClock, FiDownload, FiTag, FiCheckCircle, FiClipboard, FiFileText, 
  FiShield, FiShoppingBag, FiChevronRight, FiChevronDown, FiInfo
} from 'react-icons/fi';

interface MetricCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  iconBgColor: string;
  iconTextColor: string;
}

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  icon,
  iconBgColor,
  iconTextColor,
}) => (
  <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-200 dark:border-slate-700 p-6 hover:shadow-md transition-shadow duration-200">
    <div className="flex items-start space-x-4 mb-6">
      <div className={`p-2.5 rounded-lg ${iconBgColor} ${iconTextColor}`}>
        {icon}
      </div>
      <div>
        <p className="text-sm text-gray-500 dark:text-gray-400 font-medium">{title}</p>
        <p className="text-3xl font-bold text-gray-900 dark:text-gray-100 mt-2">{value}</p>
      </div>
    </div>
    <div className="flex items-start space-x-4">
      <div className="w-[50px]"></div>
      <div>
        <button className="text-sm text-gray-500 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 font-medium flex items-center transition-colors">
          View Details
          <FiChevronRight className="ml-1 h-4 w-4" />
        </button>
      </div>
    </div>
  </div>
);

const PurchaseCard: React.FC<{ title: string; value: string | number; icon: React.ReactNode }> = ({ title, value, icon }) => (
  <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-6 border border-green-100 dark:border-green-800/30 hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors cursor-pointer">
    <div className="flex items-start space-x-3">
      <div className="p-2 bg-green-100 dark:bg-green-800/50 text-green-600 dark:text-green-400 rounded-lg">
        {icon}
      </div>
      <div className="flex-1">
        <p className="text-xs text-green-700 dark:text-green-300 font-medium uppercase tracking-wide mb-1">{title}</p>
        <p className="text-2xl font-bold text-green-900 dark:text-green-100">{value}</p>
      </div>
    </div>
  </div>
);

interface SupplierDashboardProps {
  user: User;
}

const SupplierDashboard: React.FC<SupplierDashboardProps> = ({ user }) => {
  const [selectedCurrency, setSelectedCurrency] = React.useState<'USD' | 'UGX' | 'KES'>('USD');
  const [isCurrencyDropdownOpen, setIsCurrencyDropdownOpen] = React.useState(false);
  const currencies: Array<'USD' | 'UGX' | 'KES'> = ['USD', 'UGX', 'KES'];
  const currentDate = new Date().toLocaleDateString('en-US', { 
    weekday: 'long', 
    day: 'numeric', 
    month: 'long', 
    year: 'numeric' 
  });

  return (
    <div className="p-6 bg-gray-50 dark:bg-slate-900 min-h-screen">
      {/* Header with Greeting and Create Button */}
      <div className="flex justify-between items-start mb-8">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">Hi {user?.firstName || 'Eddie'},</h1>
          <div className="flex items-baseline space-x-2 text-sm mt-1">
            <p className="text-gray-600 dark:text-gray-400 font-medium">{currentDate}</p>
            <p className="text-gray-500 dark:text-gray-500">Here is a quick snapshot of your business.</p>
          </div>
        </div>
        <button className="bg-blue-600 text-white px-4 py-2.5 rounded-lg hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 transition-colors flex items-center font-medium shadow-sm">
          <span className="mr-2 text-lg">+</span> Create New
        </button>
      </div>

      {/* Top Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <MetricCard
          title="Pending Inquiries"
          value={35}
          icon={<FiClock className="h-6 w-6" />}
          iconBgColor="bg-orange-100 dark:bg-orange-900/30"
          iconTextColor="text-orange-600 dark:text-orange-400"
        />
        <MetricCard
          title="Sales Orders"
          value={17}
          icon={<FiDownload className="h-6 w-6" />}
          iconBgColor="bg-blue-100 dark:bg-blue-900/30"
          iconTextColor="text-blue-600 dark:text-blue-400"
        />
        <MetricCard
          title="Sales Invoices"
          value={22}
          icon={<FiTag className="h-6 w-6" />}
          iconBgColor="bg-pink-100 dark:bg-pink-900/30"
          iconTextColor="text-pink-600 dark:text-pink-400"
        />
        <MetricCard
          title="Closed Cases"
          value={35}
          icon={<FiCheckCircle className="h-6 w-6" />}
          iconBgColor="bg-green-100 dark:bg-green-900/30"
          iconTextColor="text-green-600 dark:text-green-400"
        />
      </div>

      {/* Bottom Section */}
      <div className="grid grid-cols-1 lg:grid-cols-6 gap-6">
        {/* Purchases Section */}
        <div className="lg:col-span-2">
          <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-200 dark:border-slate-700 p-8">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Purchases</h2>
            </div>
            <div className="space-y-3">
              <div className="grid grid-cols-2 gap-3">
                <PurchaseCard
                  title="Pending Requisitions"
                  value="04"
                  icon={<FiClipboard className="h-6 w-6" />}
                />
                <PurchaseCard
                  title="Quotations - In"
                  value="13"
                  icon={<FiFileText className="h-6 w-6" />}
                />
              </div>
              <div className="grid grid-cols-2 gap-3">
                <PurchaseCard
                  title="Approved"
                  value="07"
                  icon={<FiShield className="h-6 w-6" />}
                />
                <PurchaseCard
                  title="Invoices - In"
                  value="02"
                  icon={<FiFileText className="h-6 w-6" />}
                />
              </div>
              <div className="grid grid-cols-1">
                <PurchaseCard
                  title="Purchase Orders"
                  value="09"
                  icon={<FiShoppingBag className="h-6 w-6" />}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Sales Performance Section */}
        <div className="lg:col-span-4 bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-100 dark:border-slate-700 p-6">
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mr-2">Sales Performance</h2>
              <FiInfo className="h-4 w-4 text-gray-400 dark:text-gray-500 cursor-pointer" title="Sales performance details"/>
            </div>
            <div className="relative">
              <button
                onClick={() => setIsCurrencyDropdownOpen(!isCurrencyDropdownOpen)}
                className="text-sm text-gray-700 dark:text-gray-300 bg-white dark:bg-slate-700 border border-gray-200 dark:border-slate-600 hover:bg-gray-50 dark:hover:bg-slate-600 px-3 py-2 rounded-lg flex items-center font-medium shadow-sm"
              >
                {selectedCurrency}
                <FiChevronDown className={`ml-2 h-4 w-4 transition-transform duration-200 ${isCurrencyDropdownOpen ? 'rotate-180' : ''}`} />
              </button>
              {isCurrencyDropdownOpen && (
                <div className="absolute right-0 mt-2 w-20 bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-slate-600 z-10">
                  {currencies.map((currency) => (
                    <button
                      key={currency}
                      onClick={() => {
                        setSelectedCurrency(currency);
                        setIsCurrencyDropdownOpen(false);
                      }}
                      className="block w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 dark:text-gray-300 dark:hover:bg-slate-700 first:rounded-t-lg last:rounded-b-lg"
                    >
                      {currency}
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-8 mb-6">
            <div className="flex items-center space-x-2">
              <span className="h-3 w-3 bg-blue-500 rounded-full"></span>
              <span className="text-sm text-gray-600 dark:text-gray-400">Total Sales</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="h-3 w-3 bg-red-400 rounded-full"></span>
              <span className="text-sm text-gray-600 dark:text-gray-400">Total Purchases</span>
            </div>
          </div>

          <div className="flex items-start space-x-8 mb-6">
            <div>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">$254,101</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">$71,803</p>
            </div>
          </div>

          {/* Chart Placeholder */}
          <div className="h-64 bg-gradient-to-br from-blue-50 to-red-50 dark:from-slate-700/50 dark:to-slate-600/50 rounded-lg flex items-center justify-center border border-gray-100 dark:border-slate-600">
            <div className="text-center">
              <p className="text-gray-500 dark:text-gray-400 text-sm mb-2">Sales Performance Chart</p>
              <p className="text-xs text-gray-400 dark:text-gray-500">Chart visualization will be displayed here</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SupplierDashboard;
