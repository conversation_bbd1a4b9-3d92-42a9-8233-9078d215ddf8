'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { FiUsers } from 'react-icons/fi';

interface InviteSuppliersModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const InviteSuppliersModal: React.FC<InviteSuppliersModalProps> = ({ isOpen, onClose }) => {
  const [emails, setEmails] = useState('');

  const handleSendInvitation = () => {
    // TODO: Implement actual email sending logic
    console.log('Sending invitations to:', emails.split(',').map(email => email.trim()));
    onClose(); // Close modal after sending
    setEmails(''); // Reset emails
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px] p-8">
        <DialogHeader className="text-center">
          <div className="flex justify-center items-center gap-2 mb-4">
            <div className="bg-green-100 p-2 rounded-full">
              <FiUsers className="h-5 w-5 text-green-600" />
            </div>
            <div className="bg-red-100 p-2 rounded-full">
              <FiUsers className="h-5 w-5 text-red-600" />
            </div>
            <div className="bg-blue-100 p-2 rounded-full">
              <FiUsers className="h-5 w-5 text-blue-600" />
            </div>
          </div>
          <DialogTitle className="text-2xl">Invite new suppliers</DialogTitle>
          <DialogDescription className="px-4">
            Onboard or add to your existing supplier network. Collaborate with more suppliers on your projects.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid w-full items-center gap-1.5">
            <Label htmlFor="emails" className="text-left">
              Supplier email(s)
            </Label>
            <Input
              id="emails"
              type="email"
              placeholder="<EMAIL>"
              value={emails}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setEmails(e.target.value)}
            />
            <p className="text-xs text-muted-foreground">
              You can enter multiple emails, separated by a comma (,).
            </p>
          </div>
        </div>
        <DialogFooter className="sm:justify-center gap-2">
          <Button variant="outline" onClick={onClose} className="w-full sm:w-auto">
            Cancel
          </Button>
          <Button onClick={handleSendInvitation} disabled={!emails.trim()} className="w-full sm:w-auto">
            Send Invitation
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default InviteSuppliersModal;
