import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { FiPaperclip, FiX, FiSend } from "react-icons/fi";

interface ComposeEmailProps {
  onClose: () => void;
  onSend: (email: { to: string; subject: string; body: string }) => void;
}

export function ComposeEmail({ onClose, onSend }: ComposeEmailProps) {
  const [to, setTo] = useState("");
  const [subject, setSubject] = useState("");
  const [body, setBody] = useState("");
  const [isSending, setIsSending] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!to || !subject || !body) return;
    
    setIsSending(true);
    
    // Simulate API call
    setTimeout(() => {
      onSend({ to, subject, body });
      setIsSending(false);
      onClose();
    }, 1000);
  };

  return (
    <div className="fixed bottom-0 right-4 z-50 w-full max-w-md overflow-hidden rounded-t-lg bg-white shadow-xl dark:bg-gray-800">
      <div className="flex items-center justify-between bg-gray-100 px-4 py-2 dark:bg-gray-700">
        <h3 className="text-sm font-medium text-gray-900 dark:text-white">New Message</h3>
        <div className="flex space-x-2">
          <button
            type="button"
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            onClick={() => onClose()}
          >
            <FiX className="h-5 w-5" />
          </button>
        </div>
      </div>
      
      <form onSubmit={handleSubmit} className="p-4">
        <div className="space-y-4">
          <div>
            <Input
              type="email"
              placeholder="To"
              value={to}
              onChange={(e) => setTo(e.target.value)}
              required
              className="w-full"
            />
          </div>
          <div>
            <Input
              type="text"
              placeholder="Subject"
              value={subject}
              onChange={(e) => setSubject(e.target.value)}
              className="w-full"
            />
          </div>
          <div>
            <Textarea
              placeholder="Compose email..."
              rows={10}
              value={body}
              onChange={(e) => setBody(e.target.value)}
              className="min-h-[200px] w-full"
              required
            />
          </div>
          <div className="flex items-center justify-between">
            <div className="flex space-x-2">
              <Button type="submit" disabled={isSending}>
                <FiSend className="mr-2 h-4 w-4" />
                {isSending ? "Sending..." : "Send"}
              </Button>
              <Button type="button" variant="outline">
                <FiPaperclip className="h-4 w-4" />
              </Button>
            </div>
            <Button
              type="button"
              variant="ghost"
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              Discard
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
}
