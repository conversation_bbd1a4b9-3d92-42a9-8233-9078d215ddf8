import darkLogo from "@/assets/logos/dark.png";
import lightLogo from "@/assets/logos/light.png";
import Image from "next/image";
import { useTheme } from 'next-themes';

interface LogoProps {
  variant?: 'light' | 'dark';
}

export function Logo({ variant }: LogoProps) {
  const { theme } = useTheme();
  const displayVariant = variant || theme;

  return (
    <div className="relative h-12 max-w-[12.847rem]">
      {displayVariant === 'light' ? (
        <Image
          src={lightLogo}
          alt="Ascension"
          role="presentation"
          quality={100}
          priority
        />
      ) : (
        <Image
          src={darkLogo}
          alt="Ascension"
          role="presentation"
          quality={100}
          priority
        />
      )}
    </div>
  );
}
