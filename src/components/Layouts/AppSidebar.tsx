'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { FiGrid, FiBriefcase, FiUsers, FiMessageSquare, FiBarChart2, FiTrash2, FiArchive, FiSettings, FiHelpCircle, FiLogOut, FiChevronDown, FiChevronUp, FiMenu, FiX } from 'react-icons/fi';
import { Logo } from '@/components/logo';
import InviteSuppliersModal from '@/components/Modals/InviteSuppliersModal';

interface AppSidebarProps {
  isMobileOpen?: boolean;
  onMobileClose?: () => void;
}

const AppSidebar = ({ isMobileOpen = false, onMobileClose }: AppSidebarProps) => {
  const pathname = usePathname();
  const [isSuppliersOpen, setSuppliersOpen] = useState(false);
  const [isInviteModalOpen, setInviteModalOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Check if we're on mobile on initial render and when window resizes
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    // Initial check
    checkIfMobile();
    
    // Add event listener for window resize
    window.addEventListener('resize', checkIfMobile);
    
    // Cleanup
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);

  const openInviteModal = () => setInviteModalOpen(true);
  const closeInviteModal = () => setInviteModalOpen(false);

  const isActive = (path: string) => pathname === path;
  const isSuppliersActive = () => pathname.startsWith('/supplier-database');

  return (
    <aside className={`${isMobile ? 'fixed inset-y-0 left-0 z-50 transform transition-transform duration-300 ease-in-out' : 'relative'} ${isMobile && !isMobileOpen ? '-translate-x-full' : 'translate-x-0'} w-64 min-w-64 max-w-64 flex-shrink-0 bg-white dark:bg-slate-800 shadow-lg flex flex-col`}>
      <div className="h-16 flex items-center justify-between bg-[#18546c] px-6">
        <div className="flex-1 flex justify-center">
          <Logo variant="dark" />
        </div>
        {isMobile && (
          <button 
            onClick={onMobileClose} 
            className="text-white p-2 rounded-full hover:bg-black/10 focus:outline-none"
          >
            <FiX size={20} />
          </button>
        )}
      </div>
      <nav className="flex-1 px-2 py-2 space-y-1">
        <Link 
          href="/dashboard" 
          className={`flex items-center pl-4 pr-4 py-2.5 text-sm font-normal transition-all duration-200 relative ${
            isActive('/dashboard')
              ? 'bg-[#f0f7f9] text-[#18546c] border-l-4 border-[#18546c] rounded-r-md ml-0 pl-4'  
              : 'text-gray-600 hover:bg-[#f8fafb] hover:text-[#18546c] dark:text-gray-300 hover:border-l-4 hover:border-[#18546c] hover:rounded-r-md ml-0 pl-4'
          }`}
        >
          <FiGrid className={`mr-3 h-5 w-5 flex-shrink-0 ${isActive('/dashboard') ? 'text-[#18546c]' : 'text-gray-500'}`} style={{ minWidth: '20px' }} />
          <span className={`${isActive('/dashboard') ? 'font-medium' : 'font-normal'}`}>Dashboard</span>
        </Link>
        
        <Link 
          href="/projects" 
          className={`flex items-center pl-4 pr-4 py-2.5 text-sm font-normal transition-all duration-200 relative ${
            isActive('/projects')
              ? 'bg-[#f0f7f9] text-[#18546c] border-l-4 border-[#18546c] rounded-r-md ml-0 pl-4'
              : 'text-gray-600 hover:bg-[#f8fafb] hover:text-[#18546c] dark:text-gray-300 hover:border-l-4 hover:border-[#18546c] hover:rounded-r-md'
          }`}
        >
          <FiBriefcase className={`mr-3 h-5 w-5 flex-shrink-0 ${isActive('/projects') ? 'text-[#18546c]' : 'text-gray-500'}`} />
          <span className={`${isActive('/projects') ? 'font-medium' : 'font-normal'}`}>Projects</span>
        </Link>
        
        <div className="space-y-1">
          <button 
            onClick={() => setSuppliersOpen(!isSuppliersOpen)}
            className={`w-full flex items-center justify-between pl-4 pr-4 py-2.5 text-sm font-medium rounded-lg transition-colors ${
              isSuppliersActive()
                ? 'bg-[#f0f7f9] text-[#18546c] border-l-4 border-[#18546c] rounded-r-md ml-0 pl-4'
                : 'text-gray-600 hover:bg-[#f8fafb] hover:text-[#18546c] dark:text-gray-300 hover:border-l-4 hover:border-[#18546c] hover:rounded-r-md font-normal'
            }`}
          >
            <div className="flex items-center">
              <FiUsers className="mr-3 h-5 w-5 flex-shrink-0" />
              <span>Supplier Database</span>
            </div>
            <span className="ml-2 text-gray-400 dark:text-gray-500">
              {isSuppliersOpen ? <FiChevronUp className="h-4 w-4" /> : <FiChevronDown className="h-4 w-4" />}
            </span>
          </button>
          {isSuppliersOpen && (
            <div className="pl-4 mt-1 space-y-0.5 py-1">
              <button 
                onClick={openInviteModal}
                className={`block w-full text-left text-sm py-2 pl-12 pr-4 rounded-md transition-colors ${
                  isActive('/supplier-database/invite')
                    ? 'bg-[#f0f7f9] text-[#18546c] border-l-4 border-[#18546c] rounded-r-md font-medium ml-0 pl-8'
                    : 'text-gray-600 hover:bg-[#f8fafb] hover:text-[#18546c] pl-12 hover:border-l-4 hover:border-[#18546c] hover:rounded-r-md font-normal'
                }`}
              >
                Invite new suppliers
              </button>
              <Link 
                href="/supplier-database/pre-qualified" 
                className={`block text-sm py-2 pl-12 pr-4 rounded-md transition-colors ${
                  isActive('/supplier-database/pre-qualified')
                    ? 'bg-[#f0f7f9] text-[#18546c] border-l-4 border-[#18546c] rounded-r-md font-medium ml-0 pl-8'
                    : 'text-gray-600 hover:bg-[#f8fafb] hover:text-[#18546c] pl-8 hover:border-l-4 hover:border-[#18546c] hover:rounded-r-md font-normal'
                }`}
              >
                Pre-qualified suppliers
              </Link>
              <Link 
                href="/supplier-database"
                target="_blank"
                rel="noopener noreferrer"
                className={`block text-sm py-2 px-3 rounded-md transition-colors ${
                  isActive('/supplier-database')
                    ? 'bg-[#e6f0f3] text-[#18546c] border-l-4 border-[#18546c] pl-8'
                    : 'text-gray-600 hover:bg-[#f0f7f9] hover:text-[#18546c] dark:text-gray-300 dark:hover:bg-slate-700/50 hover:border-l-4 hover:border-[#18546c] pl-8'
                }`}
              >
                Supplier database
              </Link>
            </div>
          )}
        </div>

        <Link 
          href="/messages" 
          className={`flex items-center pl-4 pr-4 py-2.5 text-sm font-normal transition-all duration-200 relative ${
            isActive('/messages')
              ? 'bg-[#f0f7f9] text-[#18546c] border-l-4 border-[#18546c] rounded-r-md ml-0 pl-4'
              : 'text-gray-600 hover:bg-[#f8fafb] hover:text-[#18546c] dark:text-gray-300 dark:hover:bg-slate-700/50 hover:border-l-4 hover:border-[#18546c]'
          }`}
        >
          <FiMessageSquare className="mr-3 h-5 w-5 flex-shrink-0" />
          Messages
        </Link>
        <Link 
          href="/reports" 
          className={`flex items-center pl-4 pr-4 py-2.5 text-sm font-normal transition-all duration-200 relative ${
            isActive('/reports')
              ? 'bg-[#f0f7f9] text-[#18546c] border-l-4 border-[#18546c] rounded-r-md ml-0 pl-4'
              : 'text-gray-600 hover:bg-[#f8fafb] hover:text-[#18546c] dark:text-gray-300 dark:hover:bg-slate-700/50 hover:border-l-4 hover:border-[#18546c]'
          }`}
        >
          <FiBarChart2 className="mr-3 h-5 w-5 flex-shrink-0" />
          Reports
        </Link>
        <Link 
          href="/bin" 
          className={`flex items-center pl-4 pr-4 py-2.5 text-sm font-normal transition-all duration-200 relative ${
            isActive('/bin')
              ? 'bg-[#f0f7f9] text-[#18546c] border-l-4 border-[#18546c] rounded-r-md ml-0 pl-4'
              : 'text-gray-600 hover:bg-[#f8fafb] hover:text-[#18546c] dark:text-gray-300 dark:hover:bg-slate-700/50 hover:border-l-4 hover:border-[#18546c]'
          }`}
        >
          <FiTrash2 className="mr-3 h-5 w-5 flex-shrink-0" />
          Bin
        </Link>
        <Link 
          href="/inventory" 
          className={`flex items-center pl-4 pr-4 py-2.5 text-sm font-normal transition-all duration-200 relative ${
            isActive('/inventory')
              ? 'bg-[#f0f7f9] text-[#18546c] border-l-4 border-[#18546c] rounded-r-md ml-0 pl-4'
              : 'text-gray-600 hover:bg-[#f8fafb] hover:text-[#18546c] dark:text-gray-300 dark:hover:bg-slate-700/50 hover:border-l-4 hover:border-[#18546c]'
          }`}
        >
          <FiArchive className="mr-3 h-5 w-5 flex-shrink-0" />
          Assets & Inventory
        </Link>
      </nav>

      <div className="px-3 py-4 mt-auto border-t border-gray-200 dark:border-slate-700 space-y-1">
        <Link 
          href="/settings" 
          className={`flex items-center pl-4 pr-4 py-2.5 text-sm font-normal transition-all duration-200 relative ${
            isActive('/settings')
              ? 'bg-[#f0f7f9] text-[#18546c] border-l-4 border-[#18546c] rounded-r-md ml-0 pl-4'
              : 'text-gray-600 hover:bg-[#f8fafb] hover:text-[#18546c] dark:text-gray-300 dark:hover:bg-slate-700/50 hover:border-l-4 hover:border-[#18546c]'
          }`}
        >
          <FiSettings className="mr-3 h-5 w-5 flex-shrink-0" />
          Settings
        </Link>
        <Link 
          href="/help" 
          className={`flex items-center pl-4 pr-4 py-2.5 text-sm font-normal transition-all duration-200 relative ${
            isActive('/help')
              ? 'bg-[#f0f7f9] text-[#18546c] border-l-4 border-[#18546c] rounded-r-md ml-0 pl-4'
              : 'text-gray-600 hover:bg-[#f8fafb] hover:text-[#18546c] dark:text-gray-300 dark:hover:bg-slate-700/50 hover:border-l-4 hover:border-[#18546c]'
          }`}
        >
          <FiHelpCircle className="mr-3 h-5 w-5 flex-shrink-0" />
          Help Centre
        </Link>
      </div>

      <div className="px-3 py-4 border-t border-gray-200 dark:border-slate-700">
        <Link 
          href="/logout" 
          className="flex items-center px-4 py-3 text-sm font-medium rounded-lg text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-slate-700/50 transition-colors"
        >
          <FiLogOut className="mr-3 h-5 w-5 flex-shrink-0" />
          Logout
        </Link>
      </div>
      <InviteSuppliersModal isOpen={isInviteModalOpen} onClose={closeInviteModal} />
    </aside>
  );
};

export const MobileMenuButton = ({ onClick }: { onClick: () => void }) => {
  return (
    <button 
      onClick={onClick} 
      className="md:hidden p-2 rounded-full text-white hover:bg-black/10 focus:outline-none"
      aria-label="Open mobile menu"
    >
      <FiMenu size={20} />
    </button>
  );
};

export default AppSidebar;
