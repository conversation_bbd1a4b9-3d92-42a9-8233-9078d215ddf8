'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  FiGrid,
  FiBriefcase,
  FiUsers,
  FiMessageSquare,
  FiBarChart2,
  FiTrash2,
  FiArchive,
  FiSettings,
  FiHelpCircle,
  FiLogOut,
  FiChevronDown,
  FiChevronUp,
  FiMenu,
  FiX,
  FiPlus,
  FiEye,
  FiDatabase,
  FiUserPlus
} from 'react-icons/fi';
import { Logo } from '@/components/logo';
import InviteSuppliersModal from '@/components/Modals/InviteSuppliersModal';

interface AppSidebarProps {
  isMobileOpen?: boolean;
  onMobileClose?: () => void;
}

interface NavSubItem {
  title: string;
  href?: string;
  icon?: React.ComponentType<{ className?: string; size?: number }>;
  onClick?: () => void;
  external?: boolean;
}

interface NavItem {
  title: string;
  href?: string;
  icon: React.ComponentType<{ className?: string; size?: number }>;
  items?: NavSubItem[];
  badge?: string;
  divider?: boolean;
}

interface NavSection {
  title?: string;
  items: NavItem[];
}

const AppSidebar = ({ isMobileOpen = false, onMobileClose }: AppSidebarProps) => {
  const pathname = usePathname();
  const [expandedItems, setExpandedItems] = useState<string[]>(['Supplier Database']);
  const [isInviteModalOpen, setInviteModalOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Check if we're on mobile on initial render and when window resizes
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Initial check
    checkIfMobile();

    // Add event listener for window resize
    window.addEventListener('resize', checkIfMobile);

    // Cleanup
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);

  // Auto-expand parent menu if child is active
  useEffect(() => {
    if (pathname.startsWith('/supplier-database') && !expandedItems.includes('Supplier Database')) {
      setExpandedItems(prev => [...prev, 'Supplier Database']);
    }
  }, [pathname, expandedItems]);

  const openInviteModal = () => setInviteModalOpen(true);
  const closeInviteModal = () => setInviteModalOpen(false);

  const isActive = (path: string) => pathname === path;
  const isParentActive = (items?: NavSubItem[]) => {
    if (!items) return false;
    return items.some(item => item.href && pathname.startsWith(item.href));
  };

  const toggleExpanded = (title: string) => {
    setExpandedItems(prev =>
      prev.includes(title)
        ? prev.filter(item => item !== title)
        : [...prev, title]
    );
  };

  // Navigation data structure
  const navigationSections: NavSection[] = [
    {
      items: [
        {
          title: 'Dashboard',
          href: '/dashboard',
          icon: FiGrid,
        },
        {
          title: 'Projects',
          href: '/projects',
          icon: FiBriefcase,
        },
        {
          title: 'Supplier Database',
          icon: FiUsers,
          items: [
            {
              title: 'Invite new suppliers',
              icon: FiUserPlus,
              onClick: openInviteModal,
            },
            {
              title: 'Pre-qualified suppliers',
              href: '/supplier-database/pre-qualified',
              icon: FiEye,
            },
            {
              title: 'Supplier database',
              href: '/supplier-database',
              icon: FiDatabase,
              external: true,
            },
          ],
        },
        {
          title: 'Messages',
          href: '/messages',
          icon: FiMessageSquare,
          badge: '3',
        },
        {
          title: 'Reports',
          href: '/reports',
          icon: FiBarChart2,
        },
        {
          title: 'Bin',
          href: '/bin',
          icon: FiTrash2,
        },
        {
          title: 'Assets & Inventory',
          href: '/inventory',
          icon: FiArchive,
        },
        {
          title: 'Settings',
          href: '/settings',
          icon: FiSettings,
          divider: true,
        },
        {
          title: 'Help Centre',
          href: '/help',
          icon: FiHelpCircle,
        },
      ],
    },
  ];

  // Render navigation item
  const renderNavItem = (item: NavItem) => {
    const hasSubItems = item.items && item.items.length > 0;
    const isExpanded = expandedItems.includes(item.title);
    const itemIsActive = item.href ? isActive(item.href) : isParentActive(item.items);

    if (hasSubItems) {
      return (
        <div key={item.title} className="space-y-1">
          <button
            onClick={() => toggleExpanded(item.title)}
            className={`w-full flex items-center justify-between py-2.5 text-sm font-medium rounded-md transition-all duration-200 group mx-2 px-3 ${
              itemIsActive
                ? 'bg-[#e6f3f7] text-[#2a6e78]'
                : 'text-gray-700 hover:bg-gray-50 hover:text-[#2a6e78] dark:text-gray-300 dark:hover:bg-slate-700/50'
            }`}
          >
            <div className="flex items-center">
              <item.icon
                className={`mr-3 h-5 w-5 flex-shrink-0 transition-colors ${
                  itemIsActive ? 'text-[#2a6e78]' : 'text-gray-500 group-hover:text-[#2a6e78]'
                }`}
              />
              <span className="truncate">{item.title}</span>
              {item.badge && (
                <span className="ml-2 px-2 py-0.5 text-xs font-medium bg-red-100 text-red-800 rounded-full">
                  {item.badge}
                </span>
              )}
            </div>
            <FiChevronDown
              className={`h-4 w-4 transition-transform duration-200 ${
                isExpanded ? 'rotate-180' : ''
              } ${itemIsActive ? 'text-[#2a6e78]' : 'text-gray-400'}`}
            />
          </button>

          {isExpanded && (
            <div className="ml-6 pl-4 border-l border-gray-200 dark:border-gray-600 space-y-1">
              {item.items?.map((subItem) => renderSubItem(subItem))}
            </div>
          )}
        </div>
      );
    }

    // Regular navigation item
    if (item.href) {
      return (
        <Link
          key={item.title}
          href={item.href}
          onClick={() => isMobile && onMobileClose?.()}
          className={`flex items-center py-2.5 text-sm font-medium rounded-md transition-all duration-200 group mx-2 px-3 ${
            itemIsActive
              ? 'bg-[#e6f3f7] text-[#2a6e78]'
              : 'text-gray-700 hover:bg-gray-50 hover:text-[#2a6e78] dark:text-gray-300 dark:hover:bg-slate-700/50'
          }`}
        >
          <item.icon
            className={`mr-3 h-5 w-5 flex-shrink-0 transition-colors ${
              itemIsActive ? 'text-[#2a6e78]' : 'text-gray-500 group-hover:text-[#2a6e78]'
            }`}
          />
          <span className="truncate">{item.title}</span>
          {item.badge && (
            <span className="ml-auto px-2 py-0.5 text-xs font-medium bg-red-100 text-red-800 rounded-full">
              {item.badge}
            </span>
          )}
        </Link>
      );
    }

    return null;
  };

  // Render sub navigation item
  const renderSubItem = (subItem: NavSubItem) => {
    const subItemIsActive = subItem.href ? isActive(subItem.href) : false;

    if (subItem.onClick) {
      return (
        <button
          key={subItem.title}
          onClick={subItem.onClick}
          className={`w-full flex items-center py-2 text-sm rounded-md transition-all duration-200 group mx-2 px-3 ${
            subItemIsActive
              ? 'bg-[#e6f3f7] text-[#2a6e78] font-medium'
              : 'text-gray-600 hover:bg-gray-50 hover:text-[#2a6e78] dark:text-gray-400 dark:hover:bg-slate-700/50'
          }`}
        >
          {subItem.icon && (
            <subItem.icon
              className={`mr-3 h-4 w-4 flex-shrink-0 ${
                subItemIsActive ? 'text-[#2a6e78]' : 'text-gray-400 group-hover:text-[#2a6e78]'
              }`}
            />
          )}
          <span className="truncate">{subItem.title}</span>
        </button>
      );
    }

    if (subItem.href) {
      const linkProps = subItem.external
        ? { target: '_blank', rel: 'noopener noreferrer' }
        : { onClick: () => isMobile && onMobileClose?.() };

      return (
        <Link
          key={subItem.title}
          href={subItem.href}
          {...linkProps}
          className={`flex items-center py-2 text-sm rounded-md transition-all duration-200 group mx-2 px-3 ${
            subItemIsActive
              ? 'bg-[#e6f3f7] text-[#2a6e78] font-medium'
              : 'text-gray-600 hover:bg-gray-50 hover:text-[#2a6e78] dark:text-gray-400 dark:hover:bg-slate-700/50'
          }`}
        >
          {subItem.icon && (
            <subItem.icon
              className={`mr-3 h-4 w-4 flex-shrink-0 ${
                subItemIsActive ? 'text-[#2a6e78]' : 'text-gray-400 group-hover:text-[#2a6e78]'
              }`}
            />
          )}
          <span className="truncate">{subItem.title}</span>
        </Link>
      );
    }

    return null;
  };

  return (
    <aside className={`${isMobile ? 'fixed inset-y-0 left-0 z-50 transform transition-transform duration-300 ease-in-out' : 'relative'} ${isMobile && !isMobileOpen ? '-translate-x-full' : 'translate-x-0'} w-64 min-w-64 max-w-64 flex-shrink-0 bg-white dark:bg-slate-800 shadow-xl border-r border-gray-200 dark:border-slate-700 flex flex-col`}>
      {/* Header */}
      <div className="h-16 flex items-center justify-between bg-gradient-to-r from-[#18546c] to-[#1a5f75] px-6 shadow-sm">
        <div className="flex-1 flex justify-center">
          <Logo variant="dark" />
        </div>
        {isMobile && (
          <button
            onClick={onMobileClose}
            className="text-white p-2 rounded-full hover:bg-white/10 focus:outline-none focus:ring-2 focus:ring-white/20 transition-colors"
          >
            <FiX size={20} />
          </button>
        )}
      </div>
      {/* Navigation */}
      <div className="flex-1 flex flex-col px-3 py-4 overflow-y-auto">
        <nav className="flex-1 space-y-1">
          {navigationSections.map((section, sectionIndex) => (
            <div key={sectionIndex} className="space-y-1">
              {section.title && (
                <h3 className="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">
                  {section.title}
                </h3>
              )}
              {section.items.map((item) => (
                <React.Fragment key={item.title}>
                  {item.divider && (
                    <div className="my-4 border-t border-gray-200 dark:border-slate-700" />
                  )}
                  {renderNavItem(item)}
                </React.Fragment>
              ))}
            </div>
          ))}
        </nav>

        {/* Logout at the bottom */}
        <div className="mt-auto pt-4 border-t border-gray-200 dark:border-slate-700">
          <Link
            href="/logout"
            onClick={() => isMobile && onMobileClose?.()}
            className="flex items-center py-2.5 text-sm font-medium rounded-md transition-all duration-200 group text-gray-700 hover:bg-gray-50 hover:text-red-600 dark:text-gray-300 dark:hover:bg-slate-700/50 dark:hover:text-red-400 mx-2 px-3"
          >
            <FiLogOut className="mr-3 h-5 w-5 flex-shrink-0 transition-colors text-gray-500 group-hover:text-red-600 dark:group-hover:text-red-400" />
            <span className="truncate">Logout</span>
          </Link>
        </div>
      </div>
      {/* Invite Suppliers Modal */}
      <InviteSuppliersModal isOpen={isInviteModalOpen} onClose={closeInviteModal} />
    </aside>
  );
};

export const MobileMenuButton = ({ onClick }: { onClick: () => void }) => {
  return (
    <button
      onClick={onClick}
      className="md:hidden p-2 rounded-full text-white hover:bg-white/10 focus:outline-none focus:ring-2 focus:ring-white/20 transition-colors"
      aria-label="Open mobile menu"
    >
      <FiMenu size={20} />
    </button>
  );
};

export default AppSidebar;
